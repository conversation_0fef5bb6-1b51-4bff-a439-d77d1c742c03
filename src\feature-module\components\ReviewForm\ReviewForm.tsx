import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Button,
  Textarea,
  Input,
  Progress
} from '@heroui/react';
import { FaStar } from 'react-icons/fa';
import { FiEdit, FiUpload, FiUser } from 'react-icons/fi';
import { useAuth } from 'react-oidc-context';
import CustomButton from '../CustomButton';
import { apiClient } from '../../../api';
import {
  uploadToS3WithImageName,
  deleteFromS3ByImageName,
  getImageUrlFromName,
  getImageUrlsFromNames,
  ImageUploadResult
} from '../../frontend/Customer/aws/s3FileUpload';

interface ReviewFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit?: (reviewData: ReviewData) => Promise<void>;
  initialData?: ReviewData;
  providerId?: string;
  serviceId?: string;
  bookingId?: string;
  serviceName?: string;
  isEdit?: boolean;
}

export interface ReviewData {
  id?: string;
  providerId?: string;
  serviceId?: string;
  serviceName?: string;
  rating: number;
  title: string;
  review: string;
  images: File[];
  imageUrls: string[];
  imageNames: string[];
  date?: string;
  serviceRating?: number;
  qualityRating?: number;
  valueRating?: number;
  communicationRating?: number;
  timelinessRating?: number;
  userName?: string;
  userEmail?: string;
  userProfileImage?: string;
  isVerified?: boolean;
}

const ReviewForm = ({
  isOpen,
  onClose,
  onSubmit,
  initialData,
  providerId,
  serviceId,
  bookingId,
  serviceName,
  isEdit = false
}: ReviewFormProps) => {
  const auth = useAuth();
  const [rating, setRating] = useState<number>(0);
  const [title, setTitle] = useState<string>('');
  const [review, setReview] = useState<string>('');
  const [images, setImages] = useState<File[]>([]);
  const [imageUrls, setImageUrls] = useState<string[]>([]);
  const [imageNames, setImageNames] = useState<string[]>([]); 
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [errors, setErrors] = useState<{ title?: string; review?: string; images?: string; form?: string; rating?: string }>({});
  const [uploadingImages, setUploadingImages] = useState<boolean>(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);

  const [serviceRating, setServiceRating] = useState<number>(0);
  const [qualityRating, setQualityRating] = useState<number>(0);
  const [valueRating, setValueRating] = useState<number>(0);
  const [communicationRating, setCommunicationRating] = useState<number>(0);
  const [timelinessRating, setTimelinessRating] = useState<number>(0);

  useEffect(() => {
    if (initialData) {
      setRating(initialData.rating || 0);
      setTitle(initialData.title || (serviceName ? `Review for ${serviceName}` : ''));
      setReview(initialData.review || '');

      // Handle image data - prioritize imageNames over imageUrls
      const initImageNames = initialData.imageNames || [];
      const initImageUrls = initialData.imageUrls || [];

      setImageNames(initImageNames);

      // If we have imageNames but no imageUrls, generate URLs from names
      if (initImageNames.length > 0 && initImageUrls.length === 0) {
        const generatedUrls = getImageUrlsFromNames(initImageNames, 'review-images');
        setImageUrls(generatedUrls);
      } else {
        setImageUrls(initImageUrls);
      }

      setImages([]); 
      setServiceRating(initialData.serviceRating || 0);
      setQualityRating(initialData.qualityRating || 0);
      setValueRating(initialData.valueRating || 0);
      setCommunicationRating(initialData.communicationRating || 0);
      setTimelinessRating(initialData.timelinessRating || 0);

      console.log('Form initialized with data:', {
        imageNames: initImageNames,
        imageUrls: initImageUrls,
        generatedUrls: initImageNames.length > 0 && initImageUrls.length === 0
      });
    } else {
      // Reset form for new review
      setRating(0);
      setTitle(serviceName ? `Review for ${serviceName}` : '');
      setReview('');
      setImages([]);
      setImageUrls([]);
      setImageNames([]);
      setServiceRating(0);
      setQualityRating(0);
      setValueRating(0);
      setCommunicationRating(0);
      setTimelinessRating(0);

      console.log('Form reset for new review');
    }
    setErrors({});
  }, [isOpen, initialData, serviceName]);

  // Calculate overall rating based on the 5 specific ratings (including timelinessRating)
  useEffect(() => {
    const ratings = [serviceRating, qualityRating, valueRating, communicationRating, timelinessRating].filter(r => r > 0);
    if (ratings.length > 0) {
      const average = ratings.reduce((a, b) => a + b, 0) / ratings.length;
      setRating(Math.round(average));
      setErrors(prev => ({ ...prev, rating: undefined }));
    } else {
      setRating(0);
    }
  }, [serviceRating, qualityRating, valueRating, communicationRating, timelinessRating]);



  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files);

      const totalImages = imageUrls.length + newFiles.length;
      if (totalImages > 5) {
        setErrors(prev => ({ ...prev, images: 'You can upload a maximum of 5 images.' }));
        return;
      }

      setUploadingImages(true);
      setUploadProgress(0);
      setErrors(prev => ({ ...prev, images: undefined }));

      try {
        const uploadedUrls: string[] = [];
        const uploadedNames: string[] = [];
        const uploadResults: ImageUploadResult[] = [];

        console.log(`Starting upload of ${newFiles.length} files to S3...`);

        for (let i = 0; i < newFiles.length; i++) {
          const file = newFiles[i];
          const progress = ((i + 1) / newFiles.length) * 100;
          setUploadProgress(progress);

          console.log(`Uploading file ${i + 1}/${newFiles.length}: ${file.name}`);

          // Upload to S3 and get comprehensive result
          const uploadResult = await uploadToS3WithImageName(file, 'review-images');

          uploadedUrls.push(uploadResult.fullUrl);
          uploadedNames.push(uploadResult.imageName);
          uploadResults.push(uploadResult);

          console.log(`File uploaded successfully:`, {
            originalName: file.name,
            imageName: uploadResult.imageName,
            url: uploadResult.fullUrl
          });
        }

        // Update state with both URLs (for immediate display) and names (for backend storage)
        setImageUrls(prev => [...prev, ...uploadedUrls]);
        setImageNames(prev => [...prev, ...uploadedNames]);
        setImages(prev => [...prev, ...newFiles]);

        console.log('All files uploaded successfully:', {
          totalFiles: newFiles.length,
          imageNames: uploadedNames,
          imageUrls: uploadedUrls
        });

      } catch (error) {
        console.error('Error uploading images to S3:', error);
        setErrors(prev => ({
          ...prev,
          images: 'Failed to upload images. Please try again.'
        }));
      } finally {
        setUploadingImages(false);
        setUploadProgress(0);
      }
    }
  };

  const removeImage = async (index: number) => {
    if (index < 0 || index >= imageNames.length) {
      console.error('Invalid image index for removal:', index);
      return;
    }

    const imageName = imageNames[index];
    const imageUrl = imageUrls[index];

    console.log(`Removing image at index ${index}:`, {
      imageName,
      imageUrl
    });

    try {
      // Delete from S3 using image name (preferred method)
      if (imageName) {
        await deleteFromS3ByImageName(imageName, 'review-images');
        console.log(`Successfully deleted ${imageName} from S3`);
      }
    } catch (error) {
      console.error('Error deleting image from S3:', error);
      // Continue with removal from UI even if S3 deletion fails
      // This ensures the UI stays consistent even if there are S3 issues
    }

    // Remove from all arrays synchronously
    const newImageUrls = [...imageUrls];
    const newImageNames = [...imageNames];
    const newImages = [...images];

    newImageUrls.splice(index, 1);
    newImageNames.splice(index, 1);
    newImages.splice(index, 1);

    // Update all states
    setImageUrls(newImageUrls);
    setImageNames(newImageNames);
    setImages(newImages);

    // Clear any image-related errors
    setErrors(prev => ({ ...prev, images: undefined }));

    console.log('Image removed from UI:', {
      remainingImages: newImageNames.length,
      remainingNames: newImageNames
    });
  };

  const validateForm = () => {
    const newErrors: { title?: string; review?: string; images?: string; rating?: string } = {};

    // Check if at least one rating is provided (including timelinessRating since backend requires it)
    const hasAnyRating = [serviceRating, qualityRating, valueRating, communicationRating, timelinessRating].some(r => r > 0);
    if (!hasAnyRating) {
      newErrors.rating = 'Please provide at least one rating below (Service Quality, Work Quality, Value for Money, Communication, or Timeliness)';
    }

    // Validate overall rating (should be between 1-4 when ratings are provided)
    if (hasAnyRating && (rating < 1 || rating > 4)) {
      newErrors.rating = 'Overall rating must be between 1 and 4';
    }

    if (!title.trim()) newErrors.title = 'Please enter a review title';
    if (!review.trim()) newErrors.review = 'Please write your review';
    if (review.trim().length < 10) newErrors.review = 'Review must be at least 10 characters long';
    if (review.trim().length > 2000) newErrors.review = 'Review must be no more than 2000 characters long';

    // Photos are optional - only validate if images are uploaded (backend allows max 5 images)
    if (imageUrls.length > 5) newErrors.images = 'You can upload a maximum of 5 images.';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      // Create review data object
      const reviewData: ReviewData = {
        id: initialData?.id,
        providerId: providerId || initialData?.providerId,
        serviceId: serviceId || initialData?.serviceId,
        serviceName: serviceName || initialData?.serviceName,
        rating,
        title,
        review,
        images,
        imageUrls,
        imageNames, // Include image names
        date: initialData?.date || new Date().toISOString(),
        serviceRating,
        qualityRating,
        valueRating,
        communicationRating,
        timelinessRating,
        // Add user information from auth context
        userName: auth.user ? ((auth.user as any)?.name || (auth.user as any)?.email || 'User') : undefined,
        userEmail: auth.user ? ((auth.user as any)?.email || '') : undefined,
        userProfileImage: auth.user ? ((auth.user as any)?.picture || '') : undefined,
        isVerified: auth.user ? ((auth.user as any)?.email_verified || false) : undefined
      };

      // If onSubmit prop is provided, use it (for Reviews component)
      if (onSubmit) {
        await onSubmit(reviewData);
        onClose();
        return;
      }

      // Otherwise, submit directly to API (for other components like Bookinglist)
      const sid = serviceId || initialData?.serviceId;
      if (!sid || typeof sid !== 'string') {
        setErrors(prev => ({ ...prev, form: 'Missing or invalid serviceId' }));
        return;
      }
      if (!bookingId || typeof bookingId !== 'string') {
        setErrors(prev => ({ ...prev, form: 'Missing or invalid bookingId' }));
        return;
      }
      if (typeof rating !== 'number' || rating < 1 || rating > 4) {
        setErrors(prev => ({ ...prev, form: 'Rating must be an integer between 1 and 4' }));
        return;
      }
      if (!review || typeof review !== 'string' || review.length < 10 || review.length > 2000) {
        setErrors(prev => ({ ...prev, form: 'Review must be between 10 and 2000 characters.' }));
        return;
      }

      // Prepare review data with image names for backend storage
      const reviewPayload: any = {
        providerId: providerId || 'default-provider',
        serviceId: String(sid),
        bookingId: String(bookingId),
        rating: Math.round(rating),
        title,
        comment: review, // Backend expects comment field
        date: initialData?.date || new Date().toISOString(),
        imageNames: imageNames // Primary field: array of image names for database storage
      };

      // Backend requires all rating fields - provide defaults using overall rating if not set
      reviewPayload.serviceRating = (serviceRating >= 1 && serviceRating <= 4)
        ? Math.round(serviceRating)
        : Math.round(rating);

      reviewPayload.qualityRating = (qualityRating >= 1 && qualityRating <= 4)
        ? Math.round(qualityRating)
        : Math.round(rating);

      reviewPayload.valueRating = (valueRating >= 1 && valueRating <= 4)
        ? Math.round(valueRating)
        : Math.round(rating);

      reviewPayload.communicationRating = (communicationRating >= 1 && communicationRating <= 4)
        ? Math.round(communicationRating)
        : Math.round(rating);

      reviewPayload.timelinessRating = (timelinessRating >= 1 && timelinessRating <= 4)
        ? Math.round(timelinessRating)
        : Math.round(rating);

      // Add user information from auth context
      if (auth.user) {
        reviewPayload.userName = (auth.user as any)?.name || (auth.user as any)?.email || 'User';
        reviewPayload.userEmail = (auth.user as any)?.email || '';
        reviewPayload.userProfileImage = (auth.user as any)?.picture || '';
        reviewPayload.isVerified = (auth.user as any)?.email_verified || false;
      }

      console.log('Submitting review with payload:', {
        ...reviewPayload,
        imageCount: imageNames.length,
        imageNames: imageNames,
        ratingFields: {
          rating: reviewPayload.rating,
          serviceRating: reviewPayload.serviceRating,
          qualityRating: reviewPayload.qualityRating,
          valueRating: reviewPayload.valueRating,
          communicationRating: reviewPayload.communicationRating,
          timelinessRating: reviewPayload.timelinessRating
        },
        userInfo: {
          userName: reviewPayload.userName,
          userEmail: reviewPayload.userEmail,
          isVerified: reviewPayload.isVerified
        }
      });

      let response;
      if (isEdit && initialData?.id) {
        console.log(`Updating existing review: ${initialData.id}`);
        response = await apiClient.put(`/api/v1/reviews/${initialData.id}`, reviewPayload);
      } else {
        console.log('Creating new review');
        response = await apiClient.post(`/api/v1/reviews`, reviewPayload);
      }

      console.log('Review submission successful:', response.data);

      // Show success message
      alert(`Review ${isEdit ? 'updated' : 'submitted'} successfully!`);
      onClose();
    } catch (error) {
      console.error('Error submitting review:', error);
      setErrors(prev => ({ ...prev, form: 'Failed to submit review. Please try again.' }));
    } finally {
      setIsSubmitting(false);
    }
  };

  const StarRating = ({
    label,
    value,
    onChange
  }: {
    label: string;
    value: number;
    onChange: (val: number) => void;
  }) => (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-gray-700">{label}</label>
      <div className="flex space-x-1">
        {[1, 2, 3, 4].map((star) => (
          <FaStar
            key={star}
            className={`w-6 h-6 cursor-pointer transition-colors ${
              value >= star ? 'text-yellow-400 hover:text-yellow-500' : 'text-gray-300 hover:text-gray-400'
            }`}
            onClick={() => {
              onChange(star);
              setErrors(prev => ({ ...prev, rating: undefined }));
            }}
          />
        ))}
      </div>
      {value > 0 && (
        <p className="text-xs text-gray-600">{value} out of 4 stars</p>
      )}
    </div>
  );

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="2xl">
      <ModalContent className="rounded-xl shadow-lg max-h-[85vh] overflow-hidden flex flex-col">
        <ModalHeader className="flex flex-col gap-1 text-center bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <h2 className="text-2xl font-bold text-gray-800">{isEdit ? 'Edit Your Review' : 'Share Your Experience'}</h2>
          {serviceName && <p className="text-sm text-gray-600 font-medium">{serviceName}</p>}

          {/* User Email Display */}
          {auth.user && (auth.user as any)?.email && (
            <div className="flex items-center justify-center gap-2 mt-2 px-3 py-1 bg-white rounded-full border border-blue-200">
              <FiUser className="w-4 h-4 text-blue-500" />
              <span className="text-sm text-gray-700 font-medium">
                {(auth.user as any).name || (auth.user as any).email}
              </span>
              <span className="text-xs text-gray-500">
                ({(auth.user as any).email})
              </span>
            </div>
          )}

          <p className="text-xs text-gray-500 mt-1">Your feedback helps others make informed decisions</p>
        </ModalHeader>

        <ModalBody className="px-8 pb-6 pt-4 overflow-y-auto flex-grow">
          <div className="space-y-8">
            {errors.form && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-start">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-red-600 font-medium">{errors.form}</p>
                </div>
              </div>
            )}

            {/* Overall Rating Section */}
            <div className={`bg-gradient-to-br from-yellow-50 to-orange-50 p-4 rounded-md border border-yellow-200 ${errors.rating ? 'border-2 border-red-300' : ''}`}>
              <h3 className="text-md font-medium text-gray-800 mb-3 flex items-center justify-center">
                <FaStar className="mr-2 text-yellow-500 w-4 h-4" />
                Overall Rating
              </h3>

              <div className="flex justify-center space-x-1 mb-3">
                {[1, 2, 3, 4].map((star) => (
                  <FaStar
                    key={star}
                    className={`w-6 h-6 ${
                      rating >= star ? 'text-yellow-500' : 'text-gray-300'
                    }`}
                  />
                ))}
              </div>
              {rating > 0 ? (
                <p className="text-center text-md font-semibold text-gray-800 mb-2">
                  {rating} out of 4 stars
                </p>
              ) : (
                <p className="text-center text-md text-gray-500 mb-2">
                  Rate the aspects below to see your overall rating
                </p>
              )}
              <p className="text-xs text-gray-600 text-center">
                Automatically calculated from your ratings below
              </p>

              {errors.rating && (
                <p className="text-center text-xs text-red-600 mt-2 font-medium">{errors.rating}</p>
              )}
            </div>

            {/* Title Section */}
            <div>
              <Input
                label="Review Title"
                placeholder="e.g., 'Excellent service and professional work'"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                isInvalid={!!errors.title}
                errorMessage={errors.title}
                className="w-full"
                description="Write a brief, descriptive title for your review"
                startContent={<FiEdit className="text-gray-400" />}
              />
            </div>

            {/* Review Content Section */}
            <div>
              <Textarea
                label="Your Detailed Review"
                placeholder="Share your experience in detail. What did you like? How was the quality? Would you recommend this service?"
                value={review}
                onChange={(e) => setReview(e.target.value)}
                isInvalid={!!errors.review}
                errorMessage={errors.review}
                className="w-full"
                minRows={4}
                description={`${review.length}/2000 characters (minimum 10 characters required)`}
              />
            </div>

            {/* Image Upload Section */}
            <div className="bg-gray-50 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <FiUpload className="w-5 h-5 mr-2 text-blue-500" />
                Add Photos (Optional)
              </h3>
              <p className="text-sm text-gray-600 mb-4">Upload up to 5 images to showcase the work or service quality. Photos help other customers see the results.</p>

              {/* Upload Progress */}
              {uploadingImages && (
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-blue-600">Uploading </span>
                    <span className="text-sm text-gray-600">{Math.round(uploadProgress)}%</span>
                  </div>
                  <Progress
                    value={uploadProgress}
                    className="w-full"
                    color="primary"
                    size="sm"
                  />
                </div>
              )}

              <div className="flex flex-wrap gap-3 mb-4">
                {imageUrls.map((url, idx) => (
                  <div key={idx} className="relative group">
                    <img
                      src={url}
                      className="w-24 h-24 rounded-lg object-cover border-2 border-gray-200 shadow-sm"
                      alt={`Review image ${idx + 1}`}
                    />
                    <button
                      onClick={() => removeImage(idx)}
                      className="absolute -top-2 -right-2 text-white bg-red-500 hover:bg-red-600 rounded-full w-6 h-6 text-sm flex items-center justify-center shadow-lg opacity-0 group-hover:opacity-100 transition-opacity"
                      type="button"
                      aria-label="Remove image"
                      disabled={uploadingImages}
                    >
                      ×
                    </button>
                    {/* <div className="absolute bottom-1 left-1 bg-black bg-opacity-50 text-white text-xs px-1 rounded">
                      S3
                    </div> */}
                  </div>
                ))}
              </div>

              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-blue-400 transition-colors">
                <input
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={handleImageUpload}
                  disabled={uploadingImages}
                  className="hidden"
                  id="image-upload"
                />
                <label
                  htmlFor="image-upload"
                  className={`cursor-pointer flex flex-col items-center ${uploadingImages ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  <FiUpload className="w-8 h-8 text-gray-400 mb-2" />
                  <span className="text-sm font-medium text-gray-600">
                    {uploadingImages ? 'Uploading...' : 'Click to upload images'}
                  </span>
                  <span className="text-xs text-gray-500 mt-1">
                    PNG, JPG, GIF up to 5MB each
                  </span>
                </label>
              </div>

              {errors.images && <p className="mt-2 text-sm text-red-600">{errors.images}</p>}
            </div>

            {/* Specific Ratings Section */}
            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-lg border border-blue-100">
              <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <svg className="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                Rate Specific Aspects *
              </h3>
              <p className="text-sm text-gray-600 mb-4">Rate at least one aspect to calculate your overall rating above</p>

              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <StarRating label="Service Quality" value={serviceRating} onChange={setServiceRating} />
                <StarRating label="Work Quality" value={qualityRating} onChange={setQualityRating} />
                <StarRating label="Value for Money" value={valueRating} onChange={setValueRating} />
                <StarRating label="Communication" value={communicationRating} onChange={setCommunicationRating} />
              </div>
            </div>
          </div>
        </ModalBody>

        <ModalFooter className="px-6 pb-4">
          <Button color="danger" variant="light" onPress={onClose}>Cancel</Button>
          <CustomButton
            label={isSubmitting ? 'Submitting...' : isEdit ? 'Update Review' : 'Submit Review'}
            color="primary"
            isLoading={isSubmitting}
            isDisabled={isSubmitting}
            onPress={handleSubmit}
          />
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default ReviewForm;
